const express = require('express');
const router = express.Router();
const verifyToken = require('../../../../utils/verifyToken')
const createUploader = require('../../../../utils/upload');
const uploadProduct = createUploader('products')
const productController = require('../controllers/productController')
const optionalAuth = require('../../../../utils/optionalAuth')


// === Specific routes first ===
router.get('/favorites', verifyToken, productController.getUserFavoriteProducts);
router.get('/my-products', verifyToken, productController.getMyProducts);
router.get('/filter', productController.filterProducts);
router.get('/all', optionalAuth,productController.getAllProducts);

router.get('/:id/items', productController.getUserWithProducts);
router.get('/:productId/count', productController.getProductWithFavorites);
router.post('/:productId/favorite', verifyToken, productController.toggleFavorite);

// === Product creation/update ===
router.post('/sell', verifyToken, productController.sellProduct);
router.post('/sell-product', verifyToken, uploadProduct.array('product_photos', 5), productController.sellProductByFormdata);
router.put('/:id', verifyToken, uploadProduct.array('product_photos', 5), productController.updateProduct);
// router.patch('/:id/hide', verifyToken, productController.toggleProductVisibility);

router.post('/:productId/hide-toggle', verifyToken, productController.toggleProductVisibility);

// === Product management actions ===
router.post('/:productId/bump', verifyToken, productController.bumpProduct);
router.post('/:productId/mark-sold', verifyToken, productController.markAsSold);
router.post('/:productId/mark-reserved', verifyToken, productController.markAsReserved);
router.post('/:productId/reactivate', verifyToken, productController.reactivateProduct);

router.delete('/:id', verifyToken, productController.deleteProduct);
// router.get('/search', productController.searchProducts);

// === General product fetch ===
router.get('/:id', optionalAuth, productController.getProduct); 

module.exports = router;
