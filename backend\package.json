{"name": "souq", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node server.js", "dev": "nodemon userApp.js", "start:user": "nodemon userApp.js", "start:admin": "nodemon adminApp.js", "init-shipping": "node scripts/initializeShippingProviders.js", "add-shipping-data": "node scripts/addStaticShippingData.js", "test-shipping": "node scripts/testShippingAPI.js", "test": "node tests/testRunner.js all", "test:shipping": "node tests/testRunner.js shipping", "test:integration": "node tests/testRunner.js integration", "test:coverage": "node tests/testRunner.js coverage", "test:watch": "jest --watch"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.6.0", "bcrypt": "^6.0.0", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^8.15.0", "multer": "^2.0.2", "multer-storage-cloudinary": "^4.0.0", "node-cron": "^4.1.1", "nodemailer": "^7.0.3", "nodemon": "^3.1.10", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "react-toastify": "^11.0.5", "slugify": "^1.6.6", "socket.io": "^4.8.1", "stripe": "^14.21.0", "twilio": "^5.6.1", "ua-parser-js": "^2.0.4", "uuid": "^11.1.0"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "mongodb-memory-server": "^9.1.3", "supertest": "^6.3.3"}}