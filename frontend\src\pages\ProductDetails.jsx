import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { bumpProduct, deleteProduct, getAllCategory, getProductDetails, getUserProduct, hideProduct, markProductAsReserved, markProductAsSold, reactivateProduct } from "../api/ProductService";
import { Star, X } from "lucide-react";
import { formatDistanceToNowStrict } from 'date-fns';
import ProductGrid from "../components/Products/ProductGrid";
import AuthModal from "../components/Auth/AuthModal";
import LoginModal from "../components/Auth/LoginModal";
import ForgotPasswordModal from "../components/Auth/ForgotPasswordModal";
import SignUpModal from "../components/Auth/SignUpModal";
import { useAppContext } from "../context/AppContext";
import DeleteConfirmationModal from "../components/Products/DeleteConfirmation";
import { toast } from "react-toastify";
import LoadingSpinner from "../components/common/LoadingSpinner";
import { FaClock, FaMapMarkerAlt } from "react-icons/fa";
import PriceBreakdownModal from "../components/Products/PriceBreakDownModal";
import MakeOfferModal from "../components/Products/MakeOffer";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import ProductDetailsSkeleton from "../components/Skeleton/ProductDetailsSkeleton";
import StatusConfirmationModal from "../components/Products/StatusConfirmationModal";
import {
  setCategory,
  setSubcategory,
  setChildCategory,
  setItem,
} from "../redux/slices/CategorySlice";

const ProductDetailPage = () => {
  const { id } = useParams();
  const { t } = useTranslation()
  const navigate = useNavigate()
  const dispatch = useDispatch();
  const [product, setProduct] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [priceShowModal, setPriceShowModal] = useState(false);
  const [makeOfferModal, setMakeOfferModal] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [apiRefresh, setApiRefresh] = useState("")
  const baseURL = import.meta.env.VITE_API_BASE_URL;
  const normalizedBaseURL = baseURL.endsWith("/") ? baseURL : `${baseURL}/`;
  const normalizedURL = baseURL.endsWith('/') ? baseURL.slice(0, -1) : baseURL;
  const authUser = JSON.parse(localStorage.getItem("user"));
  const [products, setProducts] = useState([])
  const [user, setUser] = useState("")
  const [isDeleting, setIsDeleting] = useState(false);
  const [isHidden, setIsHidden] = useState(product?.hide);
  const [statusModal, setStatusModal] = useState({ isOpen: false, action: '', isLoading: false });
  const [categoryPath, setCategoryPath] = useState("");
  const categoryData = useSelector((state) => state.categoryData.data);
  const [page, setPage] = useState(1);
  const [limit] = useState(10); // fixed page size
  const [totalPages, setTotalPages] = useState(0);
  const [sortBy, setSortBy] = useState('relevance');
  const [filters, setFilters] = useState({});
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const {
    setIsAuthModalOpen,
    setAuthMode,
  } = useAppContext();

  useEffect(() => {
    if (!id) return;
    setIsLoading(true);
    getProductDetails(id)
      .then((res) => {
        setProduct(res?.data?.data?.item);
        setIsHidden(res?.data?.data?.item?.hide)
      })
      .catch((err) => {
        console.error("Error fetching product:", err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [id, apiRefresh]);

  useEffect(() => {
    const findCategoryPath = (categories, targetId) => {
      for (const category of categories) {
        const subCats = category?.subCategories || [];
        for (const sub of subCats) {
          const childCats = sub?.childCategories || [];
          for (const child of childCats) {
            const items = child?.items || [];
            for (const item of items) {
              if (item.id === targetId) {
                return [category.name, sub.name, child.name, item.name];
              }
            }
          }
        }
      }
      return null;
    };

    const fetchData = async () => {
      try {
        if (!Array.isArray(categoryData)) return;

        const pathArray = findCategoryPath(categoryData, product?.category);
        if (pathArray) {
          setCategoryPath(pathArray.join(" > "));
        } else {
          setCategoryPath("Category not found");
        }
      } catch (err) {
        console.error("Error fetching categories:", err);
        setCategoryPath("Error loading category");
      }
    };

    if (product?.category) {
      fetchData();
    }
  }, [product?.category, categoryData]);

  const handleCategoryClick = (name, level) => {
    if (!Array.isArray(categoryData)) return;

    for (const category of categoryData) {
      if (level === "main" && category.name === name) {
        dispatch(setCategory({ id: category.id, name: category.name }));
        navigate("/");
        return;
      }

      const subCats = category?.subCategories || [];
      for (const sub of subCats) {
        if (level === "sub" && sub.name === name) {
          dispatch(setSubcategory({ id: sub.id, name: sub.name }));
          navigate("/");
          return;
        }

        const childCats = sub?.childCategories || [];
        for (const child of childCats) {
          if (level === "child" && child.name === name) {
            dispatch(setChildCategory({ id: child.id, name: child.name }));
            navigate("/");
            return;
          }

          const items = child?.items || [];
          for (const item of items) {
            if (level === "item" && item.name === name) {
              dispatch(setItem({ id: item.id, name: item.name }));
              navigate("/");
              return;
            }
          }
        }
      }
    }
  };

  // useEffect(() => {
  //   if (product?.user?.id) {
  //     // setIsLoading(true); // Start loading
  //     getUserProduct(product?.user?.id)
  //       .then((res) => {
  //         const items = res?.data?.data?.items || [];
  //         setProducts(items);
  //         setUser(res?.data?.data?.user)
  //       })
  //       .catch((err) => {
  //         console.log(err, "err");
  //       })
  //       .finally(() => {
  //         // setIsLoading(false); // Stop loading
  //       });
  //   }
  // }, [product?.user?.id, apiRefresh]);

  const fetchUserProducts = async (currentPage = 1) => {
    if (!product?.user?.id) return;

    setIsLoadingMore(true);

    const query = {
      page: currentPage,
      limit,
      sortBy,
      ...filters,
    };

    try {
      const res = await getUserProduct(product.user.id, query);
      const items = res?.data?.data?.items || [];

      if (currentPage === 1) {
        setProducts(items);
      } else {
        setProducts((prev) => [...prev, ...items]); // Append new items
      }

      setUser(res?.data?.data?.user);
      setTotalPages(res?.data?.data?.totalPages || 0);
    } catch (err) {
      console.error("Failed to fetch user products:", err);
    } finally {
      setIsLoadingMore(false);
    }
  };

  useEffect(() => {
    setProducts([]); // Reset products on filter/sort change
    setPage(1);
    fetchUserProducts(1);
  }, [product?.user?.id, sortBy, filters, apiRefresh]);

  const handleLoadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    fetchUserProducts(nextPage);
  };

  const openModal = (index) => {
    setCurrentImageIndex(index);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const userNavigate = () => {
    if (authUser?.id === product?.user?.id) {
      navigate("/member-profile")
    } else {
      navigate(`/profile/${product?.user?.id}`)
    }
  }

  const userBuyNow = () => {
    if (authUser) {
      console.log('ProductDetails - userBuyNow - product:', product);
      console.log('ProductDetails - userBuyNow - product._id:', product?._id);
      console.log('ProductDetails - userBuyNow - product.id:', product?.id);
      navigate("/checkout", {
        state: {
          product: product
        }
      })
    } else {
      setAuthMode('login');
      setIsAuthModalOpen(true);
    }
  }

  if (isLoading) return <ProductDetailsSkeleton />;
  if (!product) return <div className="text-center py-20 text-gray-600">Product not found</div>;

  const photos = product?.product_photos || [];
  const maxVisibleImages = 3;
  const visibleImages = photos.slice(0, maxVisibleImages);
  const hiddenImageCount = photos.length - maxVisibleImages;

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await deleteProduct(product?.id);
      toast.success("Product deleted successfully!");
      navigate('/member-profile');
    } catch (error) {
      console.error("Delete failed:", error);
      toast.error("Something went wrong. Please try again.");
    } finally {
      setIsDeleting(false);
      setShowModal(false);
    }
  };

  const productHideUnhide = () => {
    const payload = {
      hide: !isHidden
    };
    hideProduct(product?.id, payload).then((res) => {
      console.log(res?.data?.message, "res");
      toast.success(res?.data?.message);
      setIsHidden(!isHidden);
    });
  };

  const handleBumpProduct = async (e) => {
    e.stopPropagation(); // Prevent card click navigation

    try {
      const response = await bumpProduct(product?.id);
      const resData = response;

      if (resData?.success) {
        toast.success(resData?.data?.message || "Product bumped successfully!");
        if (setApiRefresh) {
          setApiRefresh(prev => prev + 1);
        }
      } else {
        // Show error if success is false
        toast.error(resData?.error || "Failed to bump product. Please try again.");
      }
    } catch (error) {
      console.error("Bump failed:", error);
      toast.error(
        error?.response?.data?.error || "Something went wrong. Please try again."
      );
    }
  };


  const handleMarkAsSold = () => {
    setStatusModal({ isOpen: true, action: 'sold', isLoading: false });
  };

  const handleMarkAsReserved = () => {
    setStatusModal({ isOpen: true, action: 'reserved', isLoading: false });
  };

  const handleReactivateProduct = () => {
    setStatusModal({ isOpen: true, action: 'reactivate', isLoading: false });
  };

  const confirmStatusChange = async () => {
    setStatusModal(prev => ({ ...prev, isLoading: true }));

    try {
      let response;
      const { action } = statusModal;

      if (action === 'sold') {
        response = await markProductAsSold(product?.id);
        setProduct(prev => ({ ...prev, status: 'sold' }));
      } else if (action === 'reserved') {
        response = await markProductAsReserved(product?.id);
        setProduct(prev => ({ ...prev, status: 'reserved' }));
      } else if (action === 'reactivate') {
        response = await reactivateProduct(product?.id);
        setProduct(prev => ({ ...prev, status: 'active' }));
      }

      toast.success(response?.data?.message || "Product status updated successfully!");
      setApiRefresh(prev => prev + 1);
      setStatusModal({ isOpen: false, action: '', isLoading: false });
    } catch (error) {
      console.error("Status change failed:", error);
      toast.error(error?.response?.data?.error || "Failed to update product status. Please try again.");
      setStatusModal(prev => ({ ...prev, isLoading: false }));
    }
  };

  const makeOfferOpen = () => {
    if (authUser) {
      // Check if user is trying to make offer on their own product
      if (authUser.id === product.user.id) {
        toast.error("You cannot make an offer on your own product");
        return;
      }
      setMakeOfferModal(true)
    } else {
      setAuthMode('login');
      setIsAuthModalOpen(true);
    }
  }

  const messageToSeller = () => {
    if (authUser) {
      // Check if user is trying to message themselves
      if (authUser.id === product.user.id) {
        toast.error("You cannot message yourself");
        return;
      }
      // Navigate to chat layout with the product ID as a query parameter
      navigate(`/chat-layout?productId=${product.id}`)
    } else {
      setAuthMode('login');
      setIsAuthModalOpen(true);
    }
  }

  return (
    <div className="bg-white min-h-screen p-4 max-w-[1200px] mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Image Gallery */}
        <div className="col-span-2">
          {/* Main Image */}
          {photos.length > 0 && (
            <img
              // src={`${normalizedBaseURL}${photos[currentImageIndex]}`}
              src={photos[currentImageIndex]}
              alt="Main"
              className="w-full h-[400px] object-cover rounded border"
              onClick={() => openModal(currentImageIndex)}
            />
          )}

          {/* Thumbnail Row */}
          <div className="mt-2 flex gap-2">
            {visibleImages.map((img, idx) => (
              <div
                key={idx}
                className="relative cursor-pointer"
                onClick={() => openModal(idx)}
              >
                <img
                  // src={`${normalizedBaseURL}${img}`}
                  src={img}
                  alt={`Thumbnail ${idx + 1}`}
                  className="w-20 h-20 object-cover rounded border border-gray-300"
                />
                {idx === maxVisibleImages - 1 && hiddenImageCount > 0 && (
                  <div
                    className="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center text-white font-bold text-sm rounded"
                    onClick={() => openModal(idx)} // open modal from this index
                  >
                    +{hiddenImageCount}
                  </div>
                )}
              </div>
            ))}

          </div>
          <div className="mt-4 text-sm text-gray-500">
            <span className="hover:underline cursor-pointer" onClick={() => navigate("/")}>Home</span>
            {categoryPath &&
              categoryPath.split(" > ").map((part, i, arr) => {
                const level = ["main", "sub", "child", "item"][i];

                return (
                  <span key={i}>
                    {" / "}
                    <span
                      className="hover:underline cursor-pointer"
                      onClick={() => handleCategoryClick(part, level)}
                    >
                      {part}
                    </span>
                  </span>
                );
              })}

          </div>

          {/* <div className="mt-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">{t("membersItems")} ({products?.length})</h2>
            <ProductGrid products={products} user={user} apiRefresh={apiRefresh} setApiRefresh={setApiRefresh} />
          </div> */}

          <div className="mt-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              {t("membersItems")} ({products.length})
            </h2>

            {/* Sorting UI (Optional) */}
            {/* <div className="flex items-center gap-4 mb-4">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="border px-2 py-1 rounded"
              >
                <option value="relevance">Relevance</option>
                <option value="Price: Low to High">Price: Low to High</option>
                <option value="Price: High to Low">Price: High to Low</option>
                <option value="Newest first">Newest First</option>
                <option value="Oldest first">Oldest First</option>
              </select>
            </div> */}

            {/* Product Grid */}
            <ProductGrid
              products={products}
              user={user}
              apiRefresh={apiRefresh}
              setApiRefresh={setApiRefresh}
            />

            {/* Load More Button */}
            {page < totalPages && (
              <div className="flex justify-center mt-6">
                <button
                  onClick={handleLoadMore}
                  className="px-6 py-2 bg-teal-600 text-white rounded hover:bg-teal-700"
                  disabled={isLoadingMore}
                >
                  {isLoadingMore ? 'Loading...' : 'Load More'}
                </button>
              </div>
            )}
          </div>

        </div>

        {/* Product Info */}

        <div className="border rounded-md shadow-sm sticky top-4 h-fit">
          {isHidden && (
            <div className="w-full bg-gray-800 bg-opacity-75 text-white p-0 text-center text-sm font-semibold py-2 rounded-t-md mb-2">
              {t("hidden")}
            </div>
          )}
          {product.status !== "active" && (
            <div
              className={`w-full text-white p-0 text-center text-sm font-semibold py-2 rounded-t-md mb-2
      ${product.status === 'sold'
                  ? 'bg-teal-600'
                  : product.status === 'reserved'
                    ? 'bg-yellow-500 text-black'
                    : 'bg-gray-800 bg-opacity-75'
                }`}
            >
              {product.status === 'sold'
                ? 'Sold'
                : product.status === 'reserved'
                  ? 'Reserved'
                  : 'Mark as Available'}
            </div>
          )}
          <div className="p-4">
            <h1 className="text-lg font-semibold leading-5 mb-1">{product.title}</h1>
            <p className="text-sm text-gray-500 mb-2 capitalize">
              {product.size} • {product.condition} •{" "}
              <span className="text-teal-600 font-medium underline">{product.brand}</span>
            </p>

            <p className="text-sm text-gray-600">${(product.price).toFixed(2)}</p>
            <p className="text-l font-bold text-teal-600 mb-1 hover:underline cursor-pointer" onClick={() => setPriceShowModal(true)}>${Number((product.price * 1.05).toFixed(2))}</p>
            <p className="text-sm text-teal-600 mb-4 hover:underline cursor-pointer" onClick={() => setPriceShowModal(true)}>{t("includesBuyerProtection")}</p>

            <div className="text-sm text-gray-600 mb-4 space-y-1">
              <div className="flex justify-between">
                <span>{t("brand")}</span> <strong className="text-teal-600">{product.brand}</strong>
              </div>
              <div className="flex justify-between">
                <span>{t("size")}</span> <span>{product.size}</span>
              </div>
              <div className="flex justify-between">
                <span>{t("condition")}</span> <span>{product.condition}</span>
              </div>
              <div className="flex justify-between">
                <span>{t("color")}</span> <span>{product.colors}</span>
              </div>
              <div className="flex justify-between">
                <span>{t("uploaded")}</span>
                <span>{formatDistanceToNowStrict(new Date(product.createdAt), { addSuffix: true })}</span>
              </div>
            </div>

            <div className="mb-3 text-sm">
              <p className={`overflow-hidden text-gray-700 ${showFullDescription ? "" : "line-clamp-2"}`}>
                {product.description}
              </p>
              {product.description?.length > 100 && (
                <button
                  className="text-teal-600 font-semibold mt-1 flex justify-end hover:underline"
                  onClick={() => setShowFullDescription(!showFullDescription)}
                >
                  {showFullDescription ? t("showLess") : t("more")}
                </button>
              )}
            </div>

            {/* Shipping */}
            <div className="mb-3 text-sm flex justify-between">
              <span>{t("shipping")}:</span>
              <span className="text-gray-600">${(product.shipping_cost).toFixed(2)}</span>
            </div>

            {/* Actions */}

            {authUser?.id === product?.user?.id ? (
              <>
                <button
                  className={`w-full py-2 rounded-md mb-2 font-medium ${product?.status !== 'active'
                    ? 'bg-teal-600 hover:bg-teal-700 opacity-50 text-white cursor-not-allowed'
                    : 'bg-teal-600 hover:bg-teal-700 text-white'
                    }`}
                  disabled={product?.status !== 'active'}
                  onClick={handleBumpProduct}
                >
                  {t("bump")}
                </button>
                {(!product?.status || product.status === 'active') ? (
                  <>
                    <button className="text-teal-700 rounded-md font-semibold border border-teal-600 w-full py-2 mb-2" onClick={handleMarkAsSold}>
                      {t("markAsSold")}
                    </button>
                    <button className="text-teal-700 rounded-md font-semibold border border-teal-600 w-full py-2 mb-2" onClick={handleMarkAsReserved}>
                      {t("markAsReserved")}
                    </button>
                  </>
                ) : (
                  <button
                    className="text-green-700 rounded-md font-semibold border border-green-600 w-full py-2 mb-2 hover:bg-green-50"
                    onClick={handleReactivateProduct}
                  >
                    {product.status === 'sold' ? 'Mark as Unsold' :
                      product.status === 'reserved' ? 'Mark as Unreserved' :
                        'Mark as Available'}
                  </button>
                )}
                <button className="text-teal-700 rounded-md font-semibold border border-teal-600 w-full py-2 mb-2" onClick={productHideUnhide}>
                  {isHidden ? t('unhide') : t('hide')}
                </button>
                <button className="text-teal-700 rounded-md font-semibold border border-teal-600 w-full py-2 mb-2" onClick={() => navigate("/sell-now", {
                  state: {
                    product: product
                  }
                })}>
                  {t("editListing")}
                </button>
                <button className="text-red-700 border border-red-600 w-full py-2 rounded-md font-medium" onClick={() => setShowModal(true)}>
                  {t("delete")}
                </button>
              </>
            ) : (
              <>
                <button className="bg-teal-600 hover:bg-teal-700 text-white w-full py-2 rounded-md mb-2 font-medium" onClick={userBuyNow}>
                  {t("buyNow")}
                </button>
                <button className="text-teal-700 rounded-md font-semibold border border-teal-600 w-full py-2 mb-2" onClick={makeOfferOpen}>
                  {t("makeAnOffer")}
                </button>
                <button className="text-teal-700 rounded-md font-semibold border border-teal-600 w-full py-2 mb-2" onClick={messageToSeller}>
                  {t("messageSeller")}
                </button>
              </>
            )}

            {/* Buyer Protection */}
            {authUser?.id !== product?.user?.id && (
              <div className="mt-6">
                <div className="border rounded-md p-4 shadow-sm text-xs text-gray-500">
                  <strong>{t("buyerProtectionFee")}</strong>
                  <br />
                  {t("buyerProtectionInfo")}
                </div>
              </div>)}

            {/* Seller Info */}
            <div className="mt-4">
              <div
                className="border rounded-md p-4 shadow-sm cursor-pointer"
                onClick={userNavigate}
              >
                {/* Top section: profile image and user info */}
                <div className="flex items-start gap-3 border-b pb-3">
                  {/* Profile photo */}
                  {product?.user?.profile_photo ? (
                    <img
                      // src={`${normalizedURL}${product.user.profile_photo}`}
                      src={product.user.profile_photo}
                      alt={product.user.userName}
                      className="h-10 w-10 rounded-full object-cover border border-gray-100"
                    />
                  ) : (
                    <div className="bg-pink-500 text-white rounded-full h-10 w-10 flex items-center justify-center font-bold">
                      {product?.user?.userName?.[0]?.toUpperCase()}
                    </div>
                  )}

                  {/* Username and ratings */}
                  <div>
                    <p className="font-semibold">{product?.user?.userName}</p>
                    <div className="flex items-center gap-1 text-yellow-500">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star key={i} className="w-3 h-3 fill-yellow-500" />
                      ))}
                      <span className="text-sm text-gray-600">128</span>
                    </div>
                  </div>
                </div>

                {/* Location */}
                {product?.user?.country &&
                  <div className="flex items-center gap-1 text-xs text-gray-400 mt-3">
                    <FaMapMarkerAlt className="text-sm" />
                    <span>
                      {/* {product?.user?.city}, {product?.user?.country} */}
                      {product?.user?.cityShow && `${product?.user.city}, `}{product?.user?.country}
                    </span>
                  </div>}

                {/* Last seen */}
                <div className="flex items-center gap-1 text-xs text-gray-400 mt-1">
                  <FaClock className="text-sm" />
                  <span>
                    Last seen{" "}
                    {formatDistanceToNowStrict(new Date(product?.user?.lastLoginAt), {
                      addSuffix: true,
                    })}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>

      {/* Image Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 px-4">
          <div className="relative bg-white rounded-xl shadow-lg p-4 max-w-3xl w-full flex flex-col items-center">
            <button
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
              onClick={closeModal}
            >
              <X size={24} />
            </button>

            {/* Previous Button */}
            {currentImageIndex > 0 && (
              <button
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-black text-3xl z-10"
                onClick={() => setCurrentImageIndex((prev) => prev - 1)}
              >
                ‹
              </button>
            )}

            {/* Image */}
            <img
              // src={`${normalizedBaseURL}${photos[currentImageIndex]}`}
              src={photos[currentImageIndex]}
              alt="Modal View"
              className="w-full max-h-[70vh] object-contain rounded"
            />

            {/* Next Button */}
            {currentImageIndex < photos.length - 1 && (
              <button
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-black text-3xl z-10"
                onClick={() => setCurrentImageIndex((prev) => prev + 1)}
              >
                ›
              </button>
            )}
          </div>
        </div>
      )}

      <AuthModal />
      <LoginModal />
      <ForgotPasswordModal />
      <SignUpModal />
      <DeleteConfirmationModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onConfirm={handleDelete}
        isDeleting={isDeleting}
      />
      <PriceBreakdownModal
        isOpen={priceShowModal}
        onClose={() => setPriceShowModal(false)}
        itemPrice={product.price}
        protectionFee={Number((product.price * 0.05).toFixed(2))}
      />

      <MakeOfferModal product={product} onClose={() => setMakeOfferModal(false)} isOpen={makeOfferModal} />
      <StatusConfirmationModal
        isOpen={statusModal.isOpen}
        onClose={() => setStatusModal({ isOpen: false, action: '', isLoading: false })}
        onConfirm={confirmStatusChange}
        action={statusModal.action}
        productTitle={product?.title}
        currentStatus={product?.status}
        isLoading={statusModal.isLoading}
      />
    </div>
  );
};

export default ProductDetailPage;
