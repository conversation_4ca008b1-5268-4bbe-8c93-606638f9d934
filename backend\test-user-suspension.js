const mongoose = require('mongoose');
require('dotenv').config();

async function testUserSuspension() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    const User = require('./db/models/userModel');
    
    // Find a test user (or create one)
    let testUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!testUser) {
      console.log('📝 Creating test user...');
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('testpassword123', 10);
      
      testUser = new User({
        firstName: 'Test',
        lastName: 'User',
        userName: 'testuser123',
        email: '<EMAIL>',
        password: hashedPassword,
        emailVerifiedAt: new Date() // Mark as verified so they can login
      });
      
      await testUser.save();
      console.log('✅ Test user created:', testUser.email);
    } else {
      console.log('✅ Found existing test user:', testUser.email);
    }

    console.log('\n🔍 Current user status:');
    console.log('- Email:', testUser.email);
    console.log('- Suspended:', testUser.deletedAt ? 'YES' : 'NO');
    console.log('- Suspended at:', testUser.deletedAt || 'N/A');

    // Test 1: Suspend the user
    console.log('\n🚫 Testing user suspension...');
    testUser.deletedAt = new Date();
    await testUser.save();
    console.log('✅ User suspended successfully');

    // Test 2: Try to find user (should still be found but with deletedAt)
    const suspendedUser = await User.findOne({ email: '<EMAIL>' });
    console.log('📋 Suspended user check:');
    console.log('- Found:', suspendedUser ? 'YES' : 'NO');
    console.log('- Suspended:', suspendedUser?.deletedAt ? 'YES' : 'NO');

    // Test 3: Reactivate the user
    console.log('\n✅ Testing user reactivation...');
    testUser.deletedAt = null;
    await testUser.save();
    console.log('✅ User reactivated successfully');

    const reactivatedUser = await User.findOne({ email: '<EMAIL>' });
    console.log('📋 Reactivated user check:');
    console.log('- Found:', reactivatedUser ? 'YES' : 'NO');
    console.log('- Suspended:', reactivatedUser?.deletedAt ? 'YES' : 'NO');

    console.log('\n🎯 Test Summary:');
    console.log('✅ User suspension mechanism is working correctly');
    console.log('✅ Users can be suspended by setting deletedAt field');
    console.log('✅ Users can be reactivated by clearing deletedAt field');
    console.log('✅ Authentication middleware will now check for suspension');

  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the test
testUserSuspension();
