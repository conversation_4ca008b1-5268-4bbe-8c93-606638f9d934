import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { RefreshCw, Filter, ChevronLeft, ChevronRight } from 'lucide-react';
import { getWithdrawalHistory, checkWithdrawalStatus } from '../../api/WalletService';
import WithdrawalStatusCard from './WithdrawalStatusCard';

const WithdrawalHistory = ({ isOpen, onClose }) => {
  const [withdrawals, setWithdrawals] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalTransactions: 0,
    hasNext: false,
    hasPrev: false
  });
  const [filters, setFilters] = useState({
    status: '', // Explicitly set to empty string for "All"
    page: 1,
    limit: 10
  });

  // Force reset when component mounts or modal opens
  const resetToDefaults = () => {
    console.log('🔄 Resetting component to defaults');
    setFilters({
      status: '',
      page: 1,
      limit: 10
    });
    setWithdrawals([]);
    setPagination({
      currentPage: 1,
      totalPages: 1,
      totalTransactions: 0,
      hasNext: false,
      hasPrev: false
    });
  };

  // Debug: Log filter changes
  useEffect(() => {
    console.log('🔧 Filters changed:', filters);
  }, [filters]);

  useEffect(() => {
    if (isOpen) {
      console.log('📂 Modal opened, resetting and loading withdrawals...');
      resetToDefaults();
    } else {
      // Reset state when modal closes
      console.log('📂 Modal closed, resetting state...');
      setWithdrawals([]);
      setLoading(false);
      setRefreshing(false);
    }
  }, [isOpen]);

  // Separate effect for filter changes
  useEffect(() => {
    if (isOpen) {
      console.log('🔄 Filters changed, reloading data...');
      loadWithdrawals();
    }
  }, [filters]);

  const loadWithdrawals = async () => {
    console.log('🚀 loadWithdrawals() called with filters:', filters);
    setLoading(true);
    try {
      console.log('🔄 Loading withdrawals with filters:', filters);
      const response = await getWithdrawalHistory(filters);

      console.log('📥 loadWithdrawals response:', response);
      console.log('📥 Response structure check:', {
        hasSuccess: 'success' in response,
        successValue: response.success,
        hasData: 'data' in response,
        dataType: typeof response.data,
        dataKeys: response.data ? Object.keys(response.data) : 'no data'
      });

      if (response && response.success) {
        const transactions = response.data.data?.transactions || [];
        const pagination = response.data?.pagination || {};

        console.log('📊 loadWithdrawals parsed data:', {
          transactionsCount: transactions.length,
          transactions: transactions.slice(0, 2), // Show first 2 for debugging
          pagination: pagination
        });

        console.log('🔄 Setting withdrawals state to:', transactions);
        setWithdrawals(transactions);
        setPagination(pagination);

        // Force a re-render check
        setTimeout(() => {
          console.log('✅ State should be updated now. Current withdrawals length:', transactions.length);
        }, 100);

        if (transactions.length === 0) {
          console.log('⚠️ No transactions found with current filters:', filters);
        } else {
          console.log('🎉 Found transactions, should display in UI');
        }
      } else {
        console.error('❌ loadWithdrawals API returned error:', response);
        toast.error(response?.message || 'Failed to load withdrawal history');
      }
    } catch (error) {
      console.error('❌ Error in loadWithdrawals:', error);
      console.error('❌ Error stack:', error.stack);
      toast.error('Failed to load withdrawal history');
    } finally {
      console.log('🏁 loadWithdrawals finished, setting loading to false');
      setLoading(false);
    }
  };

  const refreshWithdrawalStatus = async (transactionId) => {
    setRefreshing(true);
    try {
      const response = await checkWithdrawalStatus(transactionId);
      
      if (response.success) {
        // Update the specific withdrawal in the list
        setWithdrawals(prev => prev.map(withdrawal => 
          withdrawal.transactionId === transactionId 
            ? { ...withdrawal, ...response.data }
            : withdrawal
        ));
        
        toast.success('Status updated successfully');
      } else {
        toast.error('Failed to refresh status');
      }
    } catch (error) {
      console.error('Error refreshing status:', error);
      toast.error('Failed to refresh status');
    } finally {
      setRefreshing(false);
    }
  };

  const handlePageChange = (newPage) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  const handleStatusFilter = (status) => {
    console.log('🔄 Changing filter to:', status);
    setFilters(prev => ({ ...prev, status, page: 1 }));
  };

  const getStatusFilterClass = (status) => {
    const isActive = filters.status === status;
    console.log(`🎨 Filter button "${status || 'All'}" active:`, isActive);
    return isActive
      ? 'bg-teal-600 text-white'
      : 'bg-gray-100 text-gray-700 hover:bg-gray-200';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Withdrawal History</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={loadWithdrawals}
              disabled={loading || refreshing}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
              title="Refresh"
            >
              <RefreshCw className={`w-5 h-5 ${(loading || refreshing) ? 'animate-spin' : ''}`} />
            </button>
            {/* <button
              onClick={async () => {
                console.log('🧪 Testing API directly...');
                try {
                  const testResponse = await getWithdrawalHistory({ status: '' });
                  console.log('🧪 Direct API test result:', testResponse);
                } catch (error) {
                  console.error('🧪 Direct API test error:', error);
                }
              }}
              className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
              title="Test API"
            >
              Test API
            </button> */}
            {/* <button
              onClick={() => {
                console.log('🔄 Force reset to show all');
                resetToDefaults();
              }}
              className="px-3 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
              title="Show All"
            >
              Show All
            </button>
            <button
              onClick={() => {
                console.log('🧪 Testing with dummy data');
                const dummyData = [
                  {
                    transactionId: 'TEST_123',
                    amount: 100.00,
                    currency: 'USD',
                    status: 'pending',
                    description: 'Test withdrawal',
                    bankAccount: {
                      bankName: 'Test Bank',
                      lastFourDigits: '1234'
                    },
                    createdAt: new Date().toISOString()
                  }
                ];
                console.log('🧪 Setting dummy data:', dummyData);
                setWithdrawals(dummyData);
              }}
              className="px-3 py-1 text-xs bg-purple-500 text-white rounded hover:bg-purple-600"
              title="Test Dummy Data"
            >
              Test Data
            </button> */}
            {/* <button
              onClick={() => {
                console.log('🔄 Calling loadWithdrawals() directly');
                loadWithdrawals();
              }}
              className="px-3 py-1 text-xs bg-orange-500 text-white rounded hover:bg-orange-600"
              title="Load Real Data"
            >
              Load Data
            </button> */}
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="p-6 border-b bg-gray-50">
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Filter by status:</span>
            <div className="flex space-x-2">
              <button
                onClick={() => handleStatusFilter('')}
                className={`px-3 py-1 text-sm rounded-full transition-colors ${getStatusFilterClass('')}`}
              >
                All
              </button>
              <button
                onClick={() => handleStatusFilter('pending')}
                className={`px-3 py-1 text-sm rounded-full transition-colors ${getStatusFilterClass('pending')}`}
              >
                Pending
              </button>
              <button
                onClick={() => handleStatusFilter('in_transit')}
                className={`px-3 py-1 text-sm rounded-full transition-colors ${getStatusFilterClass('in_transit')}`}
              >
                In Transit
              </button>
              <button
                onClick={() => handleStatusFilter('paid')}
                className={`px-3 py-1 text-sm rounded-full transition-colors ${getStatusFilterClass('paid')}`}
              >
                Completed
              </button>
              <button
                onClick={() => handleStatusFilter('failed')}
                className={`px-3 py-1 text-sm rounded-full transition-colors ${getStatusFilterClass('failed')}`}
              >
                Failed
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {(() => {
            console.log('🎨 Rendering content - withdrawals.length:', withdrawals.length);
            console.log('🎨 Current withdrawals:', withdrawals);
            console.log('🎨 Loading state:', loading);
            return null;
          })()}
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="w-8 h-8 animate-spin text-teal-600" />
              <span className="ml-2 text-gray-600">Loading withdrawals...</span>
            </div>
          ) : withdrawals.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-2">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-1">No withdrawals found</h3>
              <p className="text-gray-500">
                {filters.status 
                  ? `No withdrawals with status "${filters.status}" found.`
                  : 'You haven\'t made any withdrawals yet.'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {withdrawals.map((withdrawal) => (
                <WithdrawalStatusCard
                  key={withdrawal.transactionId}
                  withdrawal={withdrawal}
                  onRefresh={refreshWithdrawalStatus}
                />
              ))}
            </div>
          )}
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between p-6 border-t bg-gray-50">
            <div className="text-sm text-gray-700">
              Showing {withdrawals.length} of {pagination.totalTransactions} withdrawals
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(pagination.currentPage - 1)}
                disabled={!pagination.hasPrev}
                className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
              <span className="text-sm text-gray-700">
                Page {pagination.currentPage} of {pagination.totalPages}
              </span>
              <button
                onClick={() => handlePageChange(pagination.currentPage + 1)}
                disabled={!pagination.hasNext}
                className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WithdrawalHistory;
