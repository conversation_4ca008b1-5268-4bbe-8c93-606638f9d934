require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const passport = require('passport');
require('./utils/passport');
const connectDB = require('./db');

// Create main app
const app = express();
const PORT = process.env.PORT || 5000;
const ADMIN_PORT = process.env.ADMIN_PORT || 5001;

// Connect to database
connectDB();

// Middleware
app.use(cors({ origin: '*', credentials: true }));
app.use(express.json());
app.use(passport.initialize());

// Serve static files (images, uploads)
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// User API routes
const userRoutes = require('./app/user');
app.use('/api/user', userRoutes);

// Admin API routes
const adminRoutes = require('./app/admin');
app.use('/api/admin', adminRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    services: {
      user: 'running',
      admin: 'running'
    }
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'SOUQ Marketplace API',
    version: '1.0.0',
    endpoints: {
      user: '/api/user',
      admin: '/api/admin',
      health: '/health'
    },
    documentation: 'See API_DOCUMENTATION.md'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.originalUrl,
    method: req.method
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 SOUQ Marketplace API running on http://localhost:${PORT}`);
  console.log(`📊 User API: http://localhost:${PORT}/api/user`);
  console.log(`⚙️ Admin API: http://localhost:${PORT}/api/admin`);
  console.log(`❤️ Health Check: http://localhost:${PORT}/health`);
  console.log('');
  console.log('📋 Admin API Endpoints:');
  console.log(`   📦 Orders: http://localhost:${PORT}/api/admin/orders`);
  console.log(`   📊 Order Stats: http://localhost:${PORT}/api/admin/orders/stats`);
  console.log(`   🛡️ Escrow Orders: http://localhost:${PORT}/api/admin/orders/method/escrow`);
  console.log(`   💳 Standard Orders: http://localhost:${PORT}/api/admin/orders/method/standard`);
  console.log(`   ⭐ Ratings: http://localhost:${PORT}/api/admin/ratings`);
  console.log(`   📊 Rating Stats: http://localhost:${PORT}/api/admin/ratings/stats`);
  console.log(`   🚨 Reports: http://localhost:${PORT}/api/admin/reports`);
  console.log(`   📊 Report Stats: http://localhost:${PORT}/api/admin/reports/stats`);
  console.log('');
  console.log('💡 Note: All admin endpoints require authentication');
});

module.exports = app;
